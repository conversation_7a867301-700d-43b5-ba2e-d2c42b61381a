/**
 * SentryCoin v4.4 - On-Chain Monitoring Service
 * 
 * Monitors whale movements and exchange inflows for SPKUSDT
 * Based on forensic finding: "Exchange inflows historically precede double-digit draw-downs"
 */

import EventEmitter from 'events';
import axios from 'axios';

export default class OnChainMonitor extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.config = config;
    this.symbol = 'SPK';
    this.isRunning = false;
    
    // Whale Alert API configuration
    this.whaleAlertApiKey = process.env.WHALE_ALERT_API_KEY;
    this.whaleAlertEnabled = !!this.whaleAlertApiKey;
    
    // Monitoring thresholds
    this.whaleThreshold = parseInt(process.env.WHALE_INFLOW_THRESHOLD || '3000000'); // 3M SPK
    this.monitoringInterval = parseInt(process.env.ONCHAIN_MONITORING_INTERVAL || '60000'); // 1 minute
    
    // Known exchange addresses for SPK (would need to be populated with real addresses)
    this.exchangeAddresses = new Set([
      // Binance hot wallets
      '0x...',  // Would need real addresses
      // Coinbase
      '0x...',
      // Other major exchanges
    ]);
    
    // Tracking state
    this.lastCheckedTimestamp = Date.now();
    this.recentInflows = [];
    
    console.log('🔗 On-Chain Monitor initialized');
    console.log(`   🐋 Whale threshold: ${this.whaleThreshold} SPK`);
    console.log(`   ⏰ Check interval: ${this.monitoringInterval}ms`);
    console.log(`   📡 Whale Alert API: ${this.whaleAlertEnabled ? 'Enabled' : 'Disabled'}`);
  }

  /**
   * Start monitoring on-chain activity
   */
  async start() {
    if (this.isRunning) {
      console.log('⚠️ On-chain monitor already running');
      return;
    }

    this.isRunning = true;
    console.log('🚀 Starting on-chain monitoring...');

    // Start monitoring loop
    this.monitoringLoop();
  }

  /**
   * Stop monitoring
   */
  stop() {
    this.isRunning = false;
    console.log('⏹️ On-chain monitoring stopped');
  }

  /**
   * Main monitoring loop
   */
  async monitoringLoop() {
    while (this.isRunning) {
      try {
        await this.checkForWhaleMovements();
        await this.sleep(this.monitoringInterval);
      } catch (error) {
        console.error('❌ On-chain monitoring error:', error.message);
        await this.sleep(this.monitoringInterval * 2); // Back off on error
      }
    }
  }

  /**
   * Check for whale movements using available APIs
   */
  async checkForWhaleMovements() {
    const promises = [];

    // Check Whale Alert API if available
    if (this.whaleAlertEnabled) {
      promises.push(this.checkWhaleAlert());
    }

    // Check other sources (could add more APIs here)
    promises.push(this.checkMockWhaleData()); // Placeholder for demo

    const results = await Promise.allSettled(promises);
    
    // Process results
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        console.error(`❌ Whale check ${index} failed:`, result.reason);
      }
    });
  }

  /**
   * Check Whale Alert API for large SPK movements
   */
  async checkWhaleAlert() {
    if (!this.whaleAlertEnabled) return;

    try {
      const response = await axios.get('https://api.whale-alert.io/v1/transactions', {
        params: {
          api_key: this.whaleAlertApiKey,
          min_value: this.whaleThreshold,
          currency: 'SPK',
          start: Math.floor(this.lastCheckedTimestamp / 1000),
          limit: 100
        },
        timeout: 10000
      });

      const transactions = response.data.transactions || [];
      
      for (const tx of transactions) {
        await this.processTransaction(tx);
      }

      this.lastCheckedTimestamp = Date.now();

    } catch (error) {
      if (error.response?.status === 429) {
        console.log('⏰ Whale Alert rate limit - backing off');
      } else {
        console.error('❌ Whale Alert API error:', error.message);
      }
    }
  }

  /**
   * Process a whale transaction
   */
  async processTransaction(tx) {
    const { amount, from, to, timestamp, hash } = tx;
    
    // Check if this is an exchange inflow
    const isExchangeInflow = this.exchangeAddresses.has(to);
    const isExchangeOutflow = this.exchangeAddresses.has(from);
    
    if (isExchangeInflow && amount >= this.whaleThreshold) {
      console.log(`🐋 WHALE INFLOW: ${amount} SPK to exchange`);
      console.log(`   📝 Hash: ${hash}`);
      console.log(`   ⏰ Time: ${new Date(timestamp * 1000).toISOString()}`);
      
      const inflowData = {
        amount,
        from,
        to,
        timestamp: timestamp * 1000,
        hash,
        type: 'EXCHANGE_INFLOW'
      };
      
      this.recentInflows.push(inflowData);
      this.emit('WHALE_INFLOW', inflowData);
      
      // Clean up old inflows
      this.cleanupOldInflows();
    }
    
    if (isExchangeOutflow && amount >= this.whaleThreshold) {
      console.log(`🐋 WHALE OUTFLOW: ${amount} SPK from exchange (potentially bullish)`);
      
      this.emit('WHALE_OUTFLOW', {
        amount,
        from,
        to,
        timestamp: timestamp * 1000,
        hash,
        type: 'EXCHANGE_OUTFLOW'
      });
    }
  }

  /**
   * Mock whale data for testing (remove in production)
   */
  async checkMockWhaleData() {
    // This is for testing purposes - would be removed in production
    if (process.env.NODE_ENV === 'development' && Math.random() < 0.1) {
      const mockInflow = {
        amount: 5000000, // 5M SPK
        from: '0xmockwhale...',
        to: '0xbinance...',
        timestamp: Date.now(),
        hash: '0xmockhash...',
        type: 'EXCHANGE_INFLOW'
      };
      
      console.log('🧪 MOCK: Simulating whale inflow for testing');
      this.emit('WHALE_INFLOW', mockInflow);
    }
  }

  /**
   * Get recent whale inflows
   */
  getRecentInflows(timeWindowMs = 12 * 60 * 60 * 1000) { // 12 hours default
    const cutoff = Date.now() - timeWindowMs;
    return this.recentInflows.filter(inflow => inflow.timestamp > cutoff);
  }

  /**
   * Check if there are recent whale inflows
   */
  hasRecentWhaleInflows(timeWindowMs = 12 * 60 * 60 * 1000) {
    return this.getRecentInflows(timeWindowMs).length > 0;
  }

  /**
   * Get total recent inflow amount
   */
  getTotalRecentInflows(timeWindowMs = 12 * 60 * 60 * 1000) {
    return this.getRecentInflows(timeWindowMs)
      .reduce((total, inflow) => total + inflow.amount, 0);
  }

  /**
   * Clean up old inflow records
   */
  cleanupOldInflows() {
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    const cutoff = Date.now() - maxAge;
    
    this.recentInflows = this.recentInflows.filter(inflow => inflow.timestamp > cutoff);
  }

  /**
   * Utility function for delays
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get monitoring statistics
   */
  getStats() {
    const recentInflows = this.getRecentInflows();
    const totalRecentAmount = this.getTotalRecentInflows();
    
    return {
      isRunning: this.isRunning,
      whaleAlertEnabled: this.whaleAlertEnabled,
      recentInflowCount: recentInflows.length,
      totalRecentInflowAmount: totalRecentAmount,
      hasRecentInflows: recentInflows.length > 0,
      lastChecked: new Date(this.lastCheckedTimestamp).toISOString()
    };
  }
}
